import {
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { PrismaService } from 'src/prisma/prisma.service';
import { User, UserStatus } from '../../prisma/app/generated/prisma/client';
import * as bcrypt from 'bcrypt';
import { ROLE_NAMES } from 'src/roles/enum/roles.enum';
import { PaginateQuery } from 'src/pagination/pagination.query';
import { PaginationService } from 'src/pagination/pagination.service';
@Injectable()
export class UsersService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly paginationService: PaginationService,
  ) {}

  async create(createUserDto: CreateUserDto): Promise<User> {
    const existingUser = await this.prisma.user.findFirst({
      where: {
        OR: [
          { email: createUserDto.email },
          { phoneNumber: createUserDto.phoneNumber },
        ],
      },
    });

    if (existingUser) {
      throw new ConflictException(
        'User with this email or phone number already exists',
      );
    }

    const hashedPassword = await this.hashPassword(createUserDto.password);

    return this.prisma.user.create({
      data: {
        fullName: createUserDto.fullName,
        email: createUserDto.email,
        password: hashedPassword,
        phoneNumber: createUserDto.phoneNumber,
        organization: {
          create: {
            name: createUserDto.organizationName,
          },
        },
        roles: {
          connect: {
            name: createUserDto.roleName,
          },
        },
      },
    });
  }

  async findByEmail(email: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { email },
    });
  }

  async findByPhoneNumber(phoneNumber: string): Promise<User | null> {
    return this.prisma.user.findUnique({
      where: { phoneNumber },
    });
  }

  async findByResetToken(resetToken: string): Promise<User | null> {
    return this.prisma.user.findFirst({
      where: { resetToken },
    });
  }

  //List all users with pagination
  findAll(query: PaginateQuery) {
    const { page, perPage } = query;
    return this.paginationService.paginate<User>('user', {
      page,
      perPage,
      where: {
        deletedAt: null,
      },
      include: {
        roles: true,
        organization: true,
      },
      orderBy: {
        createdAt: 'desc',
      },
    });
  }
  async findById(id: string): Promise<User | null> {
    const user = await this.prisma.user.findUnique({
      where: { id },
      include: {
        roles: true,
        organization: true,
      },
    });

    if (!user) {
      return null;
    }

    delete user.password;
    delete user.refreshToken;
    delete user.resetToken;

    return user;
  }

  async updateRefreshToken(
    userId: string,
    refreshToken: string | null,
  ): Promise<void> {
    const hashedRefreshToken = refreshToken
      ? await this.hashPassword(refreshToken)
      : null;

    await this.prisma.user.update({
      where: { id: userId },
      data: { refreshToken: hashedRefreshToken },
    });
  }

  async validateUser(email: string, password: string): Promise<User | null> {
    const user = await this.prisma.user.findFirst({
      where: {
        email,
        deletedAt: null,
      },
      include: {
        roles: true,
      },
    });

    if (!user) {
      return null;
    }

    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      return null;
    }
    return user;
  }

  async update(userId: string, updateUserDto: UpdateUserDto) {
    const { organizationName, ...userUpdateDto } = updateUserDto;
    const user = await this.findById(userId);
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        ...userUpdateDto,
        ...(organizationName && {
          organization: {
            update: {
              name: organizationName,
            },
          },
        }),
      },
    });

    return {
      message: 'Profile Updated Sucessfully',
    };
  }

  async adminSeeder() {
    const admin = await this.prisma.user.findFirst({
      where: {
        email: '<EMAIL>',
      },
    });

    if (admin) {
      throw new ConflictException('Admin already exists');
    }

    let role = await this.prisma.role.findFirst({
      where: {
        name: ROLE_NAMES.SUPER_ADMIN,
      },
    });

    if (!role) {
      role = await this.prisma.role.create({
        data: {
          name: ROLE_NAMES.SUPER_ADMIN,
        },
      });
    }

    const password = await this.hashPassword('password');

    await this.prisma.user.create({
      data: {
        email: '<EMAIL>',
        phoneNumber: '+201010101010',
        password: password,
        status: UserStatus.active,
        emailVerifiedAt: new Date(),
        roles: {
          connect: {
            name: ROLE_NAMES.SUPER_ADMIN,
          },
        },
      },
    });

    return {
      message: 'Admin created successfully',
    };
  }

  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return bcrypt.hash(password, saltRounds);
  }

  async remove(id: string) {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException('User not found');
    }
    return this.prisma.$transaction(async (prisma) => {
      // First, get the user's company if they own one
      const user = await prisma.user.findUnique({
        where: { id },
      });

      // Delete all user verifications
      await prisma.userVerification.deleteMany({
        where: { userId: id },
      });

      // Delete the user (this will also handle the roles relationship)
      await prisma.user.delete({
        where: { id },
      });

      return {
        message: 'User deleted successfully',
      };
    });
  }
}
